#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多Agent路径规划性能测试
专注于性能测试，不进行可视化以避免matplotlib多线程问题
"""

import sys
import os
import time
import math
import random
import numpy as np
from scipy.spatial import KDTree
import matplotlib.pyplot as plt
import matplotlib.patches as patches

# 导入多Agent规划器和占用图
from src.core.pathfinding.multi_agent_planner import MultiAgentPlanner
from src.core.map.occupancy_map import OccupancyMap

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Microsoft YaHei"]
plt.rcParams["axes.unicode_minus"] = False


# 使用与单路径测试相同的模拟地图
class MockMap3D:
    def __init__(self):
        self.height = 1000
        self.width = 1000
        self.depth = 100

        # 模拟障碍物管理器
        self.obstacle_manager = MockObstacleManager()

        # 模拟网格转换器
        self.grid_converter = MockGridConverter()

        # 模拟不可通行点集合 - 创建几个禁飞区
        self.non_traversable = set()

        # 禁飞区1: 圆形
        center1 = (300, 400)
        radius1 = 50
        for y in range(center1[0] - radius1, center1[0] + radius1):
            for x in range(center1[1] - radius1, center1[1] + radius1):
                if (y - center1[0]) ** 2 + (x - center1[1]) ** 2 <= radius1**2:
                    for z in range(0, 80):
                        self.non_traversable.add((y, x, z))

        # 禁飞区2: 矩形
        for y in range(600, 700):
            for x in range(300, 450):
                for z in range(0, 80):
                    self.non_traversable.add((y, x, z))

        # 禁飞区3: 圆形
        center3 = (650, 450)
        radius3 = 40
        for y in range(center3[0] - radius3, center3[0] + radius3):
            for x in range(center3[1] - radius3, center3[1] + radius3):
                if (y - center3[0]) ** 2 + (x - center3[1]) ** 2 <= radius3**2:
                    for z in range(0, 80):
                        self.non_traversable.add((y, x, z))

    def is_traversable(self, y, x, z):
        return (y, x, z) not in self.non_traversable


class MockObstacleManager:
    def __init__(self):
        self.orbit_paths = {}
        self.orbit_quadrants = {}
        self.orbit_points_map = {}
        self.orbit_kdtrees = {}  # 新增：KD树用于快速轨道点查找

        # 创建禁飞区1的轨道 (圆形)
        center1 = (300, 400)
        radius1 = 51
        points1 = math.ceil(2 * math.pi * radius1)  # 动态计算所需点数
        orbit1 = []
        for i in range(points1):
            angle = 2 * np.pi * i / points1
            y = int(center1[0] + radius1 * np.cos(angle))
            x = int(center1[1] + radius1 * np.sin(angle))
            orbit1.append((y, x))

        self.orbit_paths["zone1"] = orbit1
        self.orbit_points_map["zone1"] = {(p[0], p[1]): i for i, p in enumerate(orbit1)}
        # 构建KD树
        if orbit1:
            coords = np.array([(point[0], point[1]) for point in orbit1])
            self.orbit_kdtrees["zone1"] = KDTree(coords)

        # 创建象限信息
        self._create_quadrants("zone1", orbit1, center1)

        # 创建禁飞区2的轨道 (矩形)
        orbit2 = []
        # 上边
        for x in range(299, 452):
            orbit2.append((701, x))
        # 右边
        for y in range(701, 598, -1):
            orbit2.append((y, 451))
        # 下边
        for x in range(451, 298, -1):
            orbit2.append((599, x))
        # 左边
        for y in range(599, 702):
            orbit2.append((y, 299))

        self.orbit_paths["zone2"] = orbit2
        self.orbit_points_map["zone2"] = {(p[0], p[1]): i for i, p in enumerate(orbit2)}
        # 构建KD树
        if orbit2:
            coords = np.array([(point[0], point[1]) for point in orbit2])
            self.orbit_kdtrees["zone2"] = KDTree(coords)

        center2 = (650, 375)  # 矩形中心
        self._create_quadrants("zone2", orbit2, center2)

        # 创建禁飞区3的轨道 (圆形)
        center3 = (650, 450)
        radius3 = 41
        points3 = math.ceil(2 * math.pi * radius3)  # 动态计算所需点数
        orbit3 = []
        for i in range(points3):
            angle = 2 * np.pi * i / points3
            y = int(center3[0] + radius3 * np.cos(angle))
            x = int(center3[1] + radius3 * np.sin(angle))
            orbit3.append((y, x))

        self.orbit_paths["zone3"] = orbit3
        self.orbit_points_map["zone3"] = {(p[0], p[1]): i for i, p in enumerate(orbit3)}
        # 构建KD树
        if orbit3:
            coords = np.array([(point[0], point[1]) for point in orbit3])
            self.orbit_kdtrees["zone3"] = KDTree(coords)

        self._create_quadrants("zone3", orbit3, center3)

    def _create_quadrants(self, zone_name, orbit_points, center):
        """创建象限信息"""
        quadrants = {
            "N": set(),
            "NE": set(),
            "E": set(),
            "SE": set(),
            "S": set(),
            "SW": set(),
            "W": set(),
            "NW": set(),
        }

        for i, (grid_y, grid_x) in enumerate(orbit_points):
            dy = grid_y - center[0]
            dx = grid_x - center[1]

            angle_rad = np.arctan2(dy, dx)
            angle_deg = np.degrees(angle_rad)
            if angle_deg < 0:
                angle_deg += 360

            if 337.5 <= angle_deg or angle_deg < 22.5:
                quadrants["E"].add(i)
            elif 22.5 <= angle_deg < 67.5:
                quadrants["NE"].add(i)
            elif 67.5 <= angle_deg < 112.5:
                quadrants["N"].add(i)
            elif 112.5 <= angle_deg < 157.5:
                quadrants["NW"].add(i)
            elif 157.5 <= angle_deg < 202.5:
                quadrants["W"].add(i)
            elif 202.5 <= angle_deg < 247.5:
                quadrants["SW"].add(i)
            elif 247.5 <= angle_deg < 292.5:
                quadrants["S"].add(i)
            elif 292.5 <= angle_deg < 337.5:
                quadrants["SE"].add(i)

        self.orbit_quadrants[zone_name] = quadrants

    def get_type_at_position(self, pos):
        y, x, z = pos

        # 检查禁飞区1 (圆形)
        center1 = (300, 400)
        if (y - center1[0]) ** 2 + (x - center1[1]) ** 2 <= 50**2:
            return ["zone1"]

        # 检查禁飞区2 (矩形)
        if 600 <= y < 700 and 300 <= x < 450:
            return ["zone2"]

        # 检查禁飞区3 (圆形)
        center3 = (650, 450)
        if (y - center3[0]) ** 2 + (x - center3[1]) ** 2 <= 40**2:
            return ["zone3"]

        return []

    def get_orbit_path(self, zone_name):
        return self.orbit_paths.get(zone_name, [])

    def get_orbit_quadrants(self, zone_name):
        return self.orbit_quadrants.get(zone_name, {})

    def get_orbit_kdtree(self, zone_name):
        """获取禁飞区的KD树用于快速轨道点查找"""
        return self.orbit_kdtrees.get(zone_name)


class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": 39.0 + y * 0.001, "lon": 116.0 + x * 0.001, "alt": z * 10}


def generate_random_tasks(
    num_tasks=100, map_size=(1000, 1000), min_height=50, seed=None
):
    """生成随机的路径规划任务"""

    if seed is not None:
        random.seed(seed)
    tasks = []

    # 避开禁飞区的安全区域
    safe_zones = [
        (0, 250, 0, 350),  # 左上角
        (0, 250, 500, 1000),  # 左下角
        (750, 1000, 0, 1000),  # 右侧
        (250, 550, 0, 250),  # 中上
        (250, 550, 500, 1000),  # 中下
    ]

    for i in range(num_tasks):
        # 随机选择起点和终点的安全区域
        start_zone = random.choice(safe_zones)
        goal_zone = random.choice(safe_zones)

        # 确保起点和终点不在同一区域
        while goal_zone == start_zone:
            goal_zone = random.choice(safe_zones)

        start = (
            random.randint(start_zone[0], start_zone[1]),
            random.randint(start_zone[2], start_zone[3]),
            random.randint(0, 30),  # 起始高度较低
        )

        goal = (
            random.randint(goal_zone[0], goal_zone[1]),
            random.randint(goal_zone[2], goal_zone[3]),
            random.randint(0, 30),  # 目标高度较低
        )

        task = {
            "agent_id": f"drone_{i:03d}",
            "start": start,
            "goal": goal,
            "min_height": min_height,
        }
        tasks.append(task)

    return tasks


def visualize_performance_results(mock_map, results, filename, test_info):
    """可视化性能测试结果"""
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))

    # 绘制禁飞区
    draw_no_fly_zones(ax)

    # 绘制轨道（淡化显示）
    draw_orbits(ax, mock_map, alpha=0.3)

    # 绘制所有成功的路径
    colors = plt.cm.tab20(np.linspace(0, 1, min(len(results), 20)))

    success_count = 0
    for i, (agent_id, result) in enumerate(results.items()):
        if result.success and result.path:
            path = result.path
            path_x = [node.x for node in path]
            path_y = [node.y for node in path]

            # 绘制路径
            color_idx = i % len(colors)
            ax.plot(
                path_x, path_y, "-", color=colors[color_idx], linewidth=1.0, alpha=0.6
            )

            # 标记起点和终点
            ax.plot(path_x[0], path_y[0], "o", color=colors[color_idx], markersize=3)
            ax.plot(path_x[-1], path_y[-1], "s", color=colors[color_idx], markersize=3)

            success_count += 1

    # 添加测试信息
    info_text = f"""
性能测试结果:
任务数量: {test_info['tasks']} 条路径
工作线程: {test_info['workers']} 个
总耗时: {test_info['total_time']:.3f}s
成功率: {test_info['success_rate']:.1f}%
吞吐量: {test_info['throughput']:.1f} 路径/秒
平均规划时间: {test_info['avg_time']:.4f}s
"""

    ax.text(
        0.02,
        0.98,
        info_text,
        transform=ax.transAxes,
        fontsize=10,
        verticalalignment="top",
        bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8),
    )

    ax.set_xlim(0, 1000)
    ax.set_ylim(0, 1000)
    ax.set_xlabel("X坐标")
    ax.set_ylabel("Y坐标")
    ax.set_title(f"多Agent路径规划性能测试 - {success_count}/{len(results)} 条路径成功")
    ax.grid(True, alpha=0.3)
    ax.set_aspect("equal")

    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches="tight")
    print(f"性能测试可视化已保存为 '{filename}'")
    plt.close()


def draw_no_fly_zones(ax):
    """绘制禁飞区"""
    # 禁飞区1 (圆形)
    circle1 = patches.Circle(
        (400, 300), 50, linewidth=2, edgecolor="red", facecolor="lightcoral", alpha=0.7
    )
    ax.add_patch(circle1)

    # 禁飞区2 (矩形)
    rect2 = patches.Rectangle(
        (300, 600),
        150,
        100,
        linewidth=2,
        edgecolor="red",
        facecolor="lightcoral",
        alpha=0.7,
    )
    ax.add_patch(rect2)

    # 禁飞区3 (圆形)
    circle3 = patches.Circle(
        (450, 650), 40, linewidth=2, edgecolor="red", facecolor="lightcoral", alpha=0.7
    )
    ax.add_patch(circle3)


def draw_orbits(ax, mock_map, alpha=0.5):
    """绘制轨道"""
    colors = ["blue", "green", "purple"]
    zone_names = ["zone1", "zone2", "zone3"]

    for i, zone_name in enumerate(zone_names):
        orbit_points = mock_map.obstacle_manager.get_orbit_path(zone_name)
        if orbit_points:
            orbit_x = [point[1] for point in orbit_points]
            orbit_y = [point[0] for point in orbit_points]
            orbit_x.append(orbit_x[0])
            orbit_y.append(orbit_y[0])

            ax.plot(orbit_x, orbit_y, "--", color=colors[i], linewidth=1, alpha=alpha)


def create_performance_summary_chart(results_table, filename):
    """创建性能汇总图表"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 按工作线程数分组数据
    worker_groups = {}
    for result in results_table:
        workers = result["workers"]
        if workers not in worker_groups:
            worker_groups[workers] = []
        worker_groups[workers].append(result)

    # 图1: 吞吐量 vs 任务数量
    for workers, group in worker_groups.items():
        tasks = [r["tasks"] for r in group]
        throughput = [r["throughput"] for r in group]
        ax1.plot(
            tasks, throughput, "o-", label=f"{workers} 线程", linewidth=2, markersize=6
        )

    ax1.set_xlabel("任务数量")
    ax1.set_ylabel("吞吐量 (路径/秒)")
    ax1.set_title("吞吐量 vs 任务数量")
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 图2: 成功率 vs 任务数量
    for workers, group in worker_groups.items():
        tasks = [r["tasks"] for r in group]
        success_rate = [r["success_rate"] for r in group]
        ax2.plot(
            tasks,
            success_rate,
            "s-",
            label=f"{workers} 线程",
            linewidth=2,
            markersize=6,
        )

    ax2.set_xlabel("任务数量")
    ax2.set_ylabel("成功率 (%)")
    ax2.set_title("成功率 vs 任务数量")
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 105)

    # 图3: 总耗时 vs 任务数量
    for workers, group in worker_groups.items():
        tasks = [r["tasks"] for r in group]
        total_time = [r["total_time"] for r in group]
        ax3.plot(
            tasks, total_time, "^-", label=f"{workers} 线程", linewidth=2, markersize=6
        )

    ax3.set_xlabel("任务数量")
    ax3.set_ylabel("总耗时 (秒)")
    ax3.set_title("总耗时 vs 任务数量")
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 图4: 平均规划时间 vs 任务数量
    for workers, group in worker_groups.items():
        tasks = [r["tasks"] for r in group]
        avg_time = [r["avg_time"] for r in group]
        ax4.plot(
            tasks, avg_time, "d-", label=f"{workers} 线程", linewidth=2, markersize=6
        )

    ax4.set_xlabel("任务数量")
    ax4.set_ylabel("平均规划时间 (秒)")
    ax4.set_title("平均规划时间 vs 任务数量")
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches="tight")
    print(f"性能汇总图表已保存为 '{filename}'")
    plt.close()


def test_performance():
    """性能测试"""
    print("=" * 80)
    print("多Agent路径规划性能测试")
    print("=" * 80)

    # 创建模拟地图
    mock_map = MockMap3D()

    # 测试不同的工作线程数
    worker_counts = [4, 8, 12, 16]
    test_scales = [100, 500, 1000]

    results_table = []

    for num_workers in worker_counts:
        print(f"\n使用 {num_workers} 个工作线程:")
        print("-" * 60)

        # 创建多Agent规划器
        planner = MultiAgentPlanner(mock_map, num_workers=num_workers)

        for num_tasks in test_scales:
            print(f"  测试规模: {num_tasks} 条路径", end=" ... ")

            # 创建占用图实例
            occupancy_map = OccupancyMap(
                (mock_map.height, mock_map.width, mock_map.depth), time_buffer=10
            )

            # 生成随机任务
            tasks = generate_random_tasks(num_tasks, seed=42)

            # 执行路径规划
            start_time = time.time()
            results = planner.plan_paths(tasks, occupancy_map)
            total_time = time.time() - start_time

            # 获取统计信息
            stats = planner.get_statistics(results)

            throughput = num_tasks / total_time
            success_rate = stats.get("success_rate", 0)
            avg_time = stats.get("average_planning_time", 0)

            print(f"完成")
            print(f"    总耗时: {total_time:.3f}s")
            print(f"    成功率: {success_rate:.1f}%")
            print(f"    平均规划时间: {avg_time:.4f}s")
            print(f"    吞吐量: {throughput:.1f} 路径/秒")

            test_info = {
                "workers": num_workers,
                "tasks": num_tasks,
                "total_time": total_time,
                "success_rate": success_rate,
                "throughput": throughput,
                "avg_time": avg_time,
            }

            results_table.append(test_info)

            # 为小规模测试生成可视化
            # if num_tasks <= 100:
            #     filename = f"performance_test_{num_workers}workers_{num_tasks}tasks.png"
            #     visualize_performance_results(mock_map, results, filename, test_info)

        # 关闭规划器
        planner.shutdown()

    # 生成性能汇总图表
    create_performance_summary_chart(results_table, "performance_summary_chart.png")

    # 打印汇总表格
    print("\n" + "=" * 80)
    print("性能汇总表")
    print("=" * 80)
    print(
        f"{'工作线程':<8} {'任务数':<8} {'总耗时(s)':<10} {'成功率(%)':<10} {'吞吐量(路径/s)':<15} {'平均时间(s)':<12}"
    )
    print("-" * 80)

    for result in results_table:
        print(
            f"{result['workers']:<8} {result['tasks']:<8} {result['total_time']:<10.3f} "
            f"{result['success_rate']:<10.1f} {result['throughput']:<15.1f} {result['avg_time']:<12.4f}"
        )

    # 找出最佳配置
    best_1000 = max(
        [r for r in results_table if r["tasks"] == 1000],
        key=lambda x: x["throughput"],
        default=None,
    )

    if best_1000:
        print(f"\n1000条路径的最佳配置:")
        print(f"  工作线程数: {best_1000['workers']}")
        print(f"  吞吐量: {best_1000['throughput']:.1f} 路径/秒")
        print(f"  总耗时: {best_1000['total_time']:.3f}s")
        print(
            f"  是否达到目标(1000路径/1秒): {'是' if best_1000['total_time'] <= 1.0 else '否'}"
        )

    print(f"\n可视化文件已生成:")
    print(f"  - 性能汇总图表: performance_summary_chart.png")
    print(f"  - 各测试场景的路径可视化: performance_test_*workers_*tasks.png")


if __name__ == "__main__":
    test_performance()
