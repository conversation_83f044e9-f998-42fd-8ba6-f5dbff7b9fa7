# -*- coding: utf-8 -*-
"""
多Agent并行路径规划器
核心功能：
1. 高性能并行批量路径规划
2. 智能负载均衡和任务分配
3. 基于轨道算法的路径规划
4. 支持1000条路径在1秒内完成
"""
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from multiprocessing import cpu_count
from typing import Dict, List, Tuple, Optional, Set, Union
from dataclasses import dataclass
from ..node_3d import GridNode3D
from ...utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class PlanningTask:
    """路径规划任务"""

    agent_id: str
    start: Tuple[int, int, int]
    goal: Tuple[int, int, int]
    min_height: int = 50
    start_time: Optional[int] = None


@dataclass
class PlanningResult:
    """路径规划结果"""

    agent_id: str
    path: Optional[List[GridNode3D]]
    error: Optional[str]
    planning_time: float
    success: bool


class ThreadSafeMapWrapper:
    """线程安全的地图包装器"""

    def __init__(self, map3d):
        """
        初始化线程安全的地图包装器
        Args:
            map3d: 原始Map3D实例
        """
        self.map3d = map3d
        self._lock = threading.RLock()

    def get_map_reference(self):
        """获取地图引用（线程安全）"""
        with self._lock:
            return self.map3d


class HighPerformanceWorkerPool:
    """高性能并行工作池"""

    def __init__(
        self, map_wrapper: ThreadSafeMapWrapper, num_workers: Optional[int] = None
    ):
        """
        初始化工作池
        Args:
            map_wrapper: 线程安全的地图包装器
            num_workers: 工作线程数，默认为CPU核心数
        """
        self.map_wrapper = map_wrapper
        self.num_workers = num_workers or min(cpu_count(), 16)  # 限制最大线程数
        self.executor = ThreadPoolExecutor(max_workers=self.num_workers)

        logger.info(f"初始化工作池，使用 {self.num_workers} 个工作线程")

    def process_tasks_parallel(self, tasks: List[PlanningTask]) -> List[PlanningResult]:
        """
        并行处理路径规划任务
        Args:
            tasks: 路径规划任务列表
        Returns:
            路径规划结果列表
        """
        if not tasks:
            return []

        # 将任务分组以优化负载均衡
        task_groups = self._distribute_tasks(tasks)

        # 提交所有任务组
        futures = []
        for task_group in task_groups:
            future = self.executor.submit(self._process_task_group, task_group)
            futures.append(future)

        # 收集结果
        results = []
        for future in as_completed(futures):
            try:
                group_results = future.result()
                results.extend(group_results)
            except Exception as e:
                logger.error(f"任务组处理失败: {e}")
                # 为失败的任务创建错误结果
                results.append(
                    PlanningResult(
                        agent_id="unknown",
                        path=None,
                        error=str(e),
                        planning_time=0.0,
                        success=False,
                    )
                )

        return results

    def _distribute_tasks(self, tasks: List[PlanningTask]) -> List[List[PlanningTask]]:
        """
        智能分配任务到工作线程
        Args:
            tasks: 任务列表
        Returns:
            分组后的任务列表
        """
        if len(tasks) <= self.num_workers:
            # 任务数少于工作线程数，每个任务一个线程
            return [[task] for task in tasks]

        # 计算每组的任务数
        tasks_per_group = len(tasks) // self.num_workers
        remainder = len(tasks) % self.num_workers

        task_groups = []
        start_idx = 0

        for i in range(self.num_workers):
            # 前remainder个组多分配一个任务
            group_size = tasks_per_group + (1 if i < remainder else 0)
            end_idx = start_idx + group_size

            if start_idx < len(tasks):
                task_groups.append(tasks[start_idx:end_idx])

            start_idx = end_idx

        return task_groups

    def _process_task_group(
        self, task_group: List[PlanningTask]
    ) -> List[PlanningResult]:
        """
        处理一组任务
        Args:
            task_group: 任务组
        Returns:
            结果列表
        """
        # from .orbit import OrbitPathFinder  # 延迟导入避免循环依赖
        from .orbit_with_astar import OrbitWithAStarPathFinder  # 延迟导入避免循环依赖

        results = []
        map_ref = self.map_wrapper.get_map_reference()

        # 为这个线程创建一个OrbitPathFinder实例
        path_finder = OrbitWithAStarPathFinder(map_ref)

        for task in task_group:
            start_time = time.time()

            try:
                # 执行路径规划
                path, error = path_finder.find_path(
                    start=task.start,
                    goal=task.goal,
                    min_height=task.min_height,
                    agent_id=task.agent_id,
                    start_time=task.start_time,
                    occupancy_map=None,  # 暂时不使用占用图避免冲突
                    constraints=None,
                )

                planning_time = time.time() - start_time

                result = PlanningResult(
                    agent_id=task.agent_id,
                    path=path,
                    error=error,
                    planning_time=planning_time,
                    success=path is not None,
                )

            except Exception as e:
                planning_time = time.time() - start_time
                logger.error(f"任务 {task.agent_id} 处理失败: {e}")

                result = PlanningResult(
                    agent_id=task.agent_id,
                    path=None,
                    error=str(e),
                    planning_time=planning_time,
                    success=False,
                )

            results.append(result)

        return results

    def process_tasks_with_occupancy(
        self, tasks: List[PlanningTask], occupancy_map
    ) -> List[PlanningResult]:
        """
        处理带occupancy_map的路径规划任务
        Args:
            tasks: 路径规划任务列表
            occupancy_map: 全局OccupancyMap实例
        Returns:
            路径规划结果列表
        """
        if not tasks:
            return []

        # 将任务分组以优化负载均衡
        task_groups = self._distribute_tasks(tasks)

        # 提交所有任务组
        futures = []
        for task_group in task_groups:
            future = self.executor.submit(
                self._process_task_group_with_occupancy, task_group, occupancy_map
            )
            futures.append(future)

        # 收集结果
        results = []
        for future in as_completed(futures):
            try:
                group_results = future.result()
                results.extend(group_results)
            except Exception as e:
                logger.error(f"带occupancy_map的任务组处理失败: {e}")
                # 为失败的任务创建错误结果
                results.append(
                    PlanningResult(
                        agent_id="unknown",
                        path=None,
                        error=str(e),
                        planning_time=0.0,
                        success=False,
                    )
                )

        return results

    def _process_task_group_with_occupancy(
        self, task_group: List[PlanningTask], occupancy_map
    ) -> List[PlanningResult]:
        """
        处理一组带occupancy_map的任务
        Args:
            task_group: 任务组
            occupancy_map: 全局OccupancyMap实例
        Returns:
            结果列表
        """
        from .orbit import OrbitPathFinder  # 延迟导入避免循环依赖

        results = []
        map_ref = self.map_wrapper.get_map_reference()

        # 为这个线程创建一个OrbitPathFinder实例
        path_finder = OrbitPathFinder(map_ref)

        for task in task_group:
            start_time = time.time()

            try:
                # 执行路径规划（带occupancy_map）
                path, error = path_finder.find_path(
                    start=task.start,
                    goal=task.goal,
                    min_height=task.min_height,
                    agent_id=task.agent_id,
                    start_time=task.start_time,
                    occupancy_map=occupancy_map,  # 传入occupancy_map进行避障
                    constraints=None,
                )

                planning_time = time.time() - start_time

                result = PlanningResult(
                    agent_id=task.agent_id,
                    path=path,
                    error=error,
                    planning_time=planning_time,
                    success=path is not None,
                )

            except Exception as e:
                planning_time = time.time() - start_time
                logger.error(f"带occupancy_map的任务 {task.agent_id} 处理失败: {e}")

                result = PlanningResult(
                    agent_id=task.agent_id,
                    path=None,
                    error=str(e),
                    planning_time=planning_time,
                    success=False,
                )

            results.append(result)

        return results

    def shutdown(self):
        """关闭工作池"""
        self.executor.shutdown(wait=True)


class MultiAgentPlanner:
    """高性能多Agent路径规划器"""

    def __init__(self, map3d, num_workers: Optional[int] = None):
        """
        初始化多Agent路径规划器
        Args:
            map3d: 3D地图实例
            num_workers: 工作线程数，默认为CPU核心数
        """
        self.map_wrapper = ThreadSafeMapWrapper(map3d)  # 提供线程安全的地图访问
        self.worker_pool = HighPerformanceWorkerPool(
            self.map_wrapper, num_workers
        )  # 管理并行任务执行的工作线程池

        logger.info("多Agent路径规划器初始化完成")

    def plan_paths(
        self,
        tasks: List[Union[PlanningTask, Dict]],
        occupancy_map,
        timeout: float = 60.0,
    ) -> Dict[str, PlanningResult]:
        """
        批量规划路径
        Args:
            tasks: 路径规划任务列表，可以是PlanningTask对象或字典
            occupancy_map: 全局OccupancyMap实例
            timeout: 超时时间（秒）
        Returns:
            {agent_id: PlanningResult} 规划结果字典
        """
        start_time = time.time()

        # 标准化任务格式
        planning_tasks = self._normalize_tasks(tasks)

        if not planning_tasks:
            logger.warning("没有有效的规划任务")
            return {}

        logger.info(f"开始规划 {len(planning_tasks)} 条路径")

        try:
            # 第一阶段：并行处理所有任务（不考虑相互冲突）
            phase1_results = self.worker_pool.process_tasks_parallel(planning_tasks)
            phase1_dict = {result.agent_id: result for result in phase1_results}

            logger.info(
                f"第一阶段完成: {sum(1 for r in phase1_results if r.success)}/{len(phase1_results)} 成功"
            )

            # 第二阶段：冲突检测与就地避障
            accepted_results, conflict_tasks = self._detect_and_resolve_conflicts(
                phase1_dict, planning_tasks, occupancy_map
            )

            logger.info(
                f"冲突检测完成: {len(accepted_results)} 接受, {len(conflict_tasks)} 需重新规划"
            )

            # 第三阶段：重新规划冲突任务
            if conflict_tasks:
                conflict_results = self._replan_conflict_tasks(
                    conflict_tasks, occupancy_map
                )
                accepted_results.update(conflict_results)
                logger.info(f"冲突重新规划完成: {len(conflict_results)} 个任务")

            # 统计最终结果
            total_time = time.time() - start_time
            success_count = sum(1 for r in accepted_results.values() if r.success)

            logger.info(
                f"路径规划完成: {success_count}/{len(planning_tasks)} 成功, "
                f"总耗时: {total_time:.3f}s, "
                f"平均耗时: {total_time/len(planning_tasks):.3f}s/路径"
            )

            return accepted_results

        except Exception as e:
            logger.error(f"批量路径规划失败: {e}")
            # 返回失败结果
            return {
                task.agent_id: PlanningResult(
                    agent_id=task.agent_id,
                    path=None,
                    error=str(e),
                    planning_time=0.0,
                    success=False,
                )
                for task in planning_tasks
            }

    def _normalize_tasks(
        self, tasks: List[Union[PlanningTask, Dict]]
    ) -> List[PlanningTask]:
        """
        标准化任务格式
        Args:
            tasks: 原始任务列表
        Returns:
            标准化的PlanningTask列表
        """
        planning_tasks = []

        for task in tasks:
            if isinstance(task, PlanningTask):
                planning_tasks.append(task)
            elif isinstance(task, dict):
                # 从字典创建PlanningTask
                planning_task = PlanningTask(
                    agent_id=task.get("agent_id", f"agent_{len(planning_tasks)}"),
                    start=task["start"],
                    goal=task["goal"],
                    min_height=task.get("min_height", 50),
                    start_time=task.get("start_time", None),
                )
                planning_tasks.append(planning_task)
            elif isinstance(task, (list, tuple)) and len(task) >= 3:
                # 从元组创建PlanningTask (agent_id, start, goal, ...)
                planning_task = PlanningTask(
                    agent_id=task[0],
                    start=task[1],
                    goal=task[2],
                    min_height=task[3] if len(task) > 3 else 50,
                    start_time=task[4] if len(task) > 4 else None,
                )
                planning_tasks.append(planning_task)
            else:
                logger.warning(f"无效的任务格式: {task}")

        return planning_tasks

    def _detect_and_resolve_conflicts(
        self,
        phase1_results: Dict[str, PlanningResult],
        original_tasks: List[PlanningTask],
        occupancy_map,
    ) -> Tuple[Dict[str, PlanningResult], List[PlanningTask]]:
        """
        冲突检测与就地避障处理
        Args:
            phase1_results: 第一阶段规划结果
            original_tasks: 原始任务列表
            occupancy_map: 全局OccupancyMap实例
        Returns:
            (接受的结果字典, 需要重新规划的任务列表)
        """
        accepted_results = {}
        conflict_agent_ids = []

        # 按优先级排序（路径长度优先，短路径先占用）
        sorted_results = self._sort_results_by_priority(phase1_results)

        for agent_id, result in sorted_results:
            if result.success and result.path:
                # 尝试预留路径（带就地垂直避障）
                success, adjusted_path = self._try_reserve_path_with_avoidance(
                    agent_id, result.path, occupancy_map
                )

                if success:
                    # 成功预留，更新结果
                    accepted_results[agent_id] = PlanningResult(
                        agent_id=agent_id,
                        path=adjusted_path,
                        error=None,
                        planning_time=result.planning_time,
                        success=True,
                    )
                    logger.debug(f"Agent {agent_id} 路径预留成功")
                else:
                    # 就地避障失败，标记为需要重新规划
                    conflict_agent_ids.append(agent_id)
                    logger.debug(f"Agent {agent_id} 路径冲突，需要重新规划")
            else:
                # 第一阶段就失败的任务，直接加入冲突列表
                conflict_agent_ids.append(agent_id)
                logger.debug(f"Agent {agent_id} 第一阶段失败，需要重新规划")

        # 找出需要重新规划的原始任务
        conflict_tasks = [
            task for task in original_tasks if task.agent_id in conflict_agent_ids
        ]

        return accepted_results, conflict_tasks

    def _sort_results_by_priority(
        self, results: Dict[str, PlanningResult]
    ) -> List[Tuple[str, PlanningResult]]:
        """
        按优先级排序结果
        优先级策略：路径长度（短路径优先）-> 规划时间（快的优先）-> agent_id
        """

        def priority_key(item):
            agent_id, result = item
            if not result.success or not result.path:
                return (float("inf"), float("inf"), agent_id)  # 失败的任务排到最后

            path_length = len(result.path)
            planning_time = result.planning_time
            return (path_length, planning_time, agent_id)

        return sorted(results.items(), key=priority_key)

    def _try_reserve_path_with_avoidance(
        self, agent_id: str, path: List[GridNode3D], occupancy_map
    ) -> Tuple[bool, Optional[List[GridNode3D]]]:
        """
        尝试预留路径，支持就地垂直避障
        Args:
            agent_id: 智能体ID
            path: 原始路径
            occupancy_map: 全局OccupancyMap实例
        Returns:
            (是否成功, 调整后的路径)
        """
        map3d = self.map_wrapper.get_map_reference()
        adjusted_path = [node for node in path]  # 创建副本

        for i, node in enumerate(adjusted_path):
            pos = (round(node.y), round(node.x), round(node.z))

            # 检查是否与其他航线冲突
            if occupancy_map._is_occupied(pos, node.t):
                # 尝试当前点的垂直避障
                success, new_node = self._try_single_point_avoidance(
                    node, map3d, occupancy_map
                )

                if success:
                    adjusted_path[i] = new_node  # 更新当前点
                    logger.debug(
                        f"Agent {agent_id} 在位置 {pos} 成功垂直避障到 {(new_node.y, new_node.x, new_node.z)}"
                    )
                    continue  # 继续检查下一个点
                else:
                    logger.debug(f"Agent {agent_id} 在位置 {pos} 垂直避障失败")
                    return False, None  # 避障失败

        # 所有点都无冲突，预留整条路径到全局occupancy_map
        occupancy_map.add_path(adjusted_path, agent_id)
        return True, adjusted_path

    def plan_single_path(
        self,
        agent_id: str,
        start: Tuple[int, int, int],
        goal: Tuple[int, int, int],
        occupancy_map,
        min_height: int = 50,
        start_time: Optional[int] = None,
    ) -> PlanningResult:
        """
        规划单条路径
        Args:
            agent_id: 智能体ID
            start: 起点坐标
            goal: 终点坐标
            occupancy_map: 全局OccupancyMap实例
            min_height: 最小巡航高度
            start_time: 起始时间
        Returns:
            路径规划结果
        """
        task = PlanningTask(
            agent_id=agent_id,
            start=start,
            goal=goal,
            min_height=min_height,
            start_time=start_time,
        )

        results = self.plan_paths([task], occupancy_map)
        return results.get(
            agent_id,
            PlanningResult(
                agent_id=agent_id,
                path=None,
                error="规划失败",
                planning_time=0.0,
                success=False,
            ),
        )

    def _try_single_point_avoidance(
        self, node: GridNode3D, map3d, occupancy_map
    ) -> Tuple[bool, Optional[GridNode3D]]:
        """
        对单个节点尝试垂直避障
        Args:
            node: 冲突的节点
            map3d: 地图实例
            occupancy_map: 占用图实例
        Returns:
            (是否成功, 调整后的节点)
        """
        # 尝试向上移动 (+1)
        up_pos = (round(node.y), round(node.x), round(node.z) + 1)
        if self._is_valid_against_map3d(
            up_pos, map3d
        ) and not occupancy_map._is_occupied(up_pos, node.t):
            new_node = GridNode3D(
                node.y, node.x, node.z + 1, node.t, node.g, node.h, node.f, node.parent
            )
            return True, new_node

        # 尝试向下移动 (-1)
        down_pos = (round(node.y), round(node.x), round(node.z) - 1)
        if self._is_valid_against_map3d(
            down_pos, map3d, check_min_height=True
        ) and not occupancy_map._is_occupied(down_pos, node.t):
            new_node = GridNode3D(
                node.y, node.x, node.z - 1, node.t, node.g, node.h, node.f, node.parent
            )
            return True, new_node

        # 向上向下都不行
        return False, None

    def _is_valid_against_map3d(
        self, pos: Tuple[int, int, int], map3d, check_min_height: bool = False
    ) -> bool:
        """
        检查位置是否与Map3D中的固定障碍物冲突
        Args:
            pos: 位置坐标 (y, x, z)
            map3d: 地图实例
            check_min_height: 是否检查最小高度限制
        Returns:
            是否有效（无冲突）
        """
        y, x, z = pos

        # 检查地图边界
        if not (
            0 <= y < map3d.height and 0 <= x < map3d.width and 0 <= z < map3d.depth
        ):
            return False

        # 检查固定障碍物
        if not map3d.is_traversable(y, x, z):
            return False

        # 检查最小高度（如果需要）
        if check_min_height:
            # 这里可以根据实际需求设置最小高度，暂时使用固定值
            min_height = 10  # 可以从配置或参数获取
            if z < min_height:
                return False

        return True

    def _replan_conflict_tasks(
        self, conflict_tasks: List[PlanningTask], occupancy_map
    ) -> Dict[str, PlanningResult]:
        """
        重新规划冲突任务
        Args:
            conflict_tasks: 需要重新规划的任务列表
            occupancy_map: 已经包含accepted路径的全局OccupancyMap实例
        Returns:
            重新规划的结果字典
        """
        if not conflict_tasks:
            return {}

        logger.info(f"开始重新规划 {len(conflict_tasks)} 个冲突任务")

        # 使用带occupancy_map的工作池重新规划
        # 这次OrbitPathFinder会通过occupancy_map.check_collision进行避障
        conflict_results = self.worker_pool.process_tasks_with_occupancy(
            conflict_tasks, occupancy_map
        )

        # 转换为字典格式
        result_dict = {}
        for result in conflict_results:
            if result.success and result.path:
                # 成功的路径需要加入occupancy_map
                occupancy_map.add_path(result.path, result.agent_id)
                logger.debug(f"冲突任务 {result.agent_id} 重新规划成功")
            else:
                logger.debug(f"冲突任务 {result.agent_id} 重新规划失败: {result.error}")

            result_dict[result.agent_id] = result

        return result_dict

    def get_statistics(self, results: Dict[str, PlanningResult]) -> Dict:
        """
        获取规划统计信息
        Args:
            results: 规划结果字典
        Returns:
            统计信息字典
        """
        if not results:
            return {}

        success_results = [r for r in results.values() if r.success]
        failed_results = [r for r in results.values() if not r.success]

        planning_times = [r.planning_time for r in results.values()]

        stats = {
            "total_tasks": len(results),
            "successful_tasks": len(success_results),
            "failed_tasks": len(failed_results),
            "success_rate": len(success_results) / len(results) * 100,
            "total_planning_time": sum(planning_times),
            "average_planning_time": sum(planning_times) / len(planning_times),
            "min_planning_time": min(planning_times),
            "max_planning_time": max(planning_times),
        }

        if success_results:
            success_times = [r.planning_time for r in success_results]
            stats.update(
                {
                    "average_success_time": sum(success_times) / len(success_times),
                    "min_success_time": min(success_times),
                    "max_success_time": max(success_times),
                }
            )

        return stats

    def shutdown(self):
        """关闭规划器"""
        self.worker_pool.shutdown()
        logger.info("多Agent路径规划器已关闭")
