#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轨道路径规划算法可视化测试
"""

import sys
import os
import time

# sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 确保项目根目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.animation import FuncAnimation
from scipy.spatial import KDTree
import math

# 导入轨道路径规划器
# from src.core.pathfinding.orbit import OrbitPathFinder
from src.core.pathfinding.orbit_with_astar import OrbitWithAStarPathFinder

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Microsoft YaHei"]
plt.rcParams["axes.unicode_minus"] = False


# 创建一个模拟的地图类用于测试
class MockMap3D:
    def __init__(self):
        self.height = 1000
        self.width = 1000
        self.depth = 100

        # 模拟障碍物管理器
        self.obstacle_manager = MockObstacleManager()

        # 模拟网格转换器
        self.grid_converter = MockGridConverter()

        # 模拟不可通行点集合 - 创建几个禁飞区
        self.non_traversable = set()

        # 禁飞区1: 圆形
        center1 = (300, 400)
        radius1 = 50
        for y in range(center1[0] - radius1, center1[0] + radius1):
            for x in range(center1[1] - radius1, center1[1] + radius1):
                if (y - center1[0]) ** 2 + (x - center1[1]) ** 2 <= radius1**2:
                    for z in range(0, 80):
                        self.non_traversable.add((y, x, z))

        # 禁飞区2: 矩形
        for y in range(600, 700):
            for x in range(300, 450):
                for z in range(0, 80):
                    self.non_traversable.add((y, x, z))

        # 禁飞区3: 圆形
        center3 = (650, 450)
        radius3 = 40
        for y in range(center3[0] - radius3, center3[0] + radius3):
            for x in range(center3[1] - radius3, center3[1] + radius3):
                if (y - center3[0]) ** 2 + (x - center3[1]) ** 2 <= radius3**2:
                    for z in range(0, 80):
                        self.non_traversable.add((y, x, z))

    def is_traversable(self, y, x, z):
        return (y, x, z) not in self.non_traversable


class MockObstacleManager:
    def __init__(self):
        self.orbit_paths = {}
        self.orbit_quadrants = {}
        self.orbit_points_map = {}  # 新增：轨道点位置到索引的映射
        self.orbit_kdtrees = {}  # 新增：KD树用于快速轨道点查找

        # 创建禁飞区1的轨道 (圆形)
        center1 = (300, 400)
        radius1 = 52  # 禁飞区半径50 + 1
        points1 = math.ceil(2 * math.pi * radius1)  # 动态计算所需点数
        orbit1 = []
        for i in range(points1):
            angle = 2 * math.pi * i / points1
            y = int(center1[0] + radius1 * math.cos(angle))
            x = int(center1[1] + radius1 * math.sin(angle))
            orbit1.append((y, x))

        self.orbit_paths["zone1"] = orbit1
        self.orbit_points_map["zone1"] = {(p[0], p[1]): i for i, p in enumerate(orbit1)}
        # 构建KD树
        if orbit1:
            coords = np.array([(point[0], point[1]) for point in orbit1])
            self.orbit_kdtrees["zone1"] = KDTree(coords)
        # 按照与矩形相同的方式计算象限
        quadrants = {
            "N": set(),
            "NE": set(),
            "E": set(),
            "SE": set(),
            "S": set(),
            "SW": set(),
            "W": set(),
            "NW": set(),
        }

        # 计算每个点相对于圆心的角度
        for i, (grid_y, grid_x) in enumerate(orbit1):
            dy = grid_y - center1[0]
            dx = grid_x - center1[1]

            # 计算角度
            angle_rad = math.atan2(dy, dx)  # Y轴向下为正，与数学坐标系相同
            angle_deg = math.degrees(angle_rad)
            if angle_deg < 0:
                angle_deg += 360

            # 根据角度划分象限
            if 337.5 <= angle_deg or angle_deg < 22.5:
                quadrants["E"].add(i)  # 0° 指向东
            elif 22.5 <= angle_deg < 67.5:
                quadrants["NE"].add(i)  # 45° 指向东北
            elif 67.5 <= angle_deg < 112.5:
                quadrants["N"].add(i)  # 90° 指向北
            elif 112.5 <= angle_deg < 157.5:
                quadrants["NW"].add(i)  # 135° 指向西北
            elif 157.5 <= angle_deg < 202.5:
                quadrants["W"].add(i)  # 180° 指向西
            elif 202.5 <= angle_deg < 247.5:
                quadrants["SW"].add(i)  # 225° 指向西南
            elif 247.5 <= angle_deg < 292.5:
                quadrants["S"].add(i)  # 270° 指向南
            elif 292.5 <= angle_deg < 337.5:
                quadrants["SE"].add(i)  # 315° 指向东南

        self.orbit_quadrants["zone1"] = quadrants

        # 创建禁飞区2的轨道 (矩形)
        orbit2 = []
        # 上边 (y较大)
        for x in range(299, 452):
            orbit2.append((701, x))  # 从左到右
        # 右边
        for y in range(701, 598, -1):  # 从上到下
            orbit2.append((y, 451))
        # 下边 (y较小)
        for x in range(451, 298, -1):  # 从右到左
            orbit2.append((599, x))
        # 左边
        for y in range(599, 702):  # 从下到上
            orbit2.append((y, 299))

        self.orbit_paths["zone2"] = orbit2
        self.orbit_points_map["zone2"] = {(p[0], p[1]): i for i, p in enumerate(orbit2)}
        # 构建KD树
        if orbit2:
            coords = np.array([(point[0], point[1]) for point in orbit2])
            self.orbit_kdtrees["zone2"] = KDTree(coords)

        # 计算矩形中心点
        center_y = (600 + 700) / 2  # y: 600-700的中点
        center_x = (300 + 450) / 2  # x: 300-450的中点

        # 初始化象限
        quadrants = {
            "N": set(),
            "NE": set(),
            "E": set(),
            "SE": set(),
            "S": set(),
            "SW": set(),
            "W": set(),
            "NW": set(),
        }

        # 根据每个点相对于中心点的角度进行象限划分
        for i, (grid_y, grid_x) in enumerate(orbit2):
            # 计算相对于中心的偏移
            dy = grid_y - center_y
            dx = grid_x - center_x

            # 计算角度
            angle_rad = math.atan2(dy, dx)  # Y轴向下为正，与数学坐标系相同
            angle_deg = math.degrees(angle_rad)
            if angle_deg < 0:
                angle_deg += 360

            # 根据角度划分象限
            if 337.5 <= angle_deg or angle_deg < 22.5:
                quadrants["E"].add(i)  # 0° 指向东
            elif 22.5 <= angle_deg < 67.5:
                quadrants["NE"].add(i)  # 45° 指向东北
            elif 67.5 <= angle_deg < 112.5:
                quadrants["N"].add(i)  # 90° 指向北
            elif 112.5 <= angle_deg < 157.5:
                quadrants["NW"].add(i)  # 135° 指向西北
            elif 157.5 <= angle_deg < 202.5:
                quadrants["W"].add(i)  # 180° 指向西
            elif 202.5 <= angle_deg < 247.5:
                quadrants["SW"].add(i)  # 225° 指向西南
            elif 247.5 <= angle_deg < 292.5:
                quadrants["S"].add(i)  # 270° 指向南
            elif 292.5 <= angle_deg < 337.5:
                quadrants["SE"].add(i)  # 315° 指向东南

        self.orbit_quadrants["zone2"] = quadrants

        # 创建禁飞区3的轨道 (圆形)
        center3 = (650, 450)
        radius3 = 42  # 禁飞区半径40 + 1
        points3 = math.ceil(2 * math.pi * radius3)  # 动态计算所需点数
        orbit3 = []
        for i in range(points3):
            angle = 2 * math.pi * i / points3
            y = int(center3[0] + radius3 * math.cos(angle))
            x = int(center3[1] + radius3 * math.sin(angle))
            orbit3.append((y, x))

        self.orbit_paths["zone3"] = orbit3
        self.orbit_points_map["zone3"] = {(p[0], p[1]): i for i, p in enumerate(orbit3)}
        # 构建KD树
        if orbit3:
            coords = np.array([(point[0], point[1]) for point in orbit3])
            self.orbit_kdtrees["zone3"] = KDTree(coords)

        # 按照与其他禁飞区相同的方式计算象限
        quadrants = {
            "N": set(),
            "NE": set(),
            "E": set(),
            "SE": set(),
            "S": set(),
            "SW": set(),
            "W": set(),
            "NW": set(),
        }

        # 计算每个点相对于圆心的角度
        for i, (grid_y, grid_x) in enumerate(orbit3):
            dy = grid_y - center3[0]
            dx = grid_x - center3[1]

            # 计算角度
            angle_rad = math.atan2(dy, dx)  # Y轴向下为正，与数学坐标系相同
            angle_deg = math.degrees(angle_rad)
            if angle_deg < 0:
                angle_deg += 360

            # 根据角度划分象限
            if 337.5 <= angle_deg or angle_deg < 22.5:
                quadrants["E"].add(i)  # 0° 指向东
            elif 22.5 <= angle_deg < 67.5:
                quadrants["NE"].add(i)  # 45° 指向东北
            elif 67.5 <= angle_deg < 112.5:
                quadrants["N"].add(i)  # 90° 指向北
            elif 112.5 <= angle_deg < 157.5:
                quadrants["NW"].add(i)  # 135° 指向西北
            elif 157.5 <= angle_deg < 202.5:
                quadrants["W"].add(i)  # 180° 指向西
            elif 202.5 <= angle_deg < 247.5:
                quadrants["SW"].add(i)  # 225° 指向西南
            elif 247.5 <= angle_deg < 292.5:
                quadrants["S"].add(i)  # 270° 指向南
            elif 292.5 <= angle_deg < 337.5:
                quadrants["SE"].add(i)  # 315° 指向东南

        self.orbit_quadrants["zone3"] = quadrants

    def get_type_at_position(self, pos):
        y, x, z = pos

        # 检查禁飞区1 (圆形)
        center1 = (300, 400)
        if (y - center1[0]) ** 2 + (x - center1[1]) ** 2 <= 50**2:
            return ["zone1"]

        # 检查禁飞区2 (矩形)
        if 600 <= y < 700 and 300 <= x < 450:
            return ["zone2"]

        # 检查禁飞区3 (圆形)
        center3 = (650, 450)
        if (y - center3[0]) ** 2 + (x - center3[1]) ** 2 <= 40**2:
            return ["zone3"]

        return []

    def get_orbit_path(self, zone_name):
        return self.orbit_paths.get(zone_name, [])

    def get_orbit_quadrants(self, zone_name):
        return self.orbit_quadrants.get(zone_name, {})

    def get_orbit_kdtree(self, zone_name):
        """获取禁飞区的KD树用于快速轨道点查找"""
        return self.orbit_kdtrees.get(zone_name)


class MockGridConverter:
    def relative_to_geo(self, y, x, z):
        return {"lat": 39.0 + y * 0.001, "lon": 116.0 + x * 0.001, "alt": z * 10}


def visualize_map_and_orbits(mock_map):
    """可视化地图和轨道"""
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))

    # 绘制禁飞区
    # 禁飞区1 (圆形)
    circle1 = patches.Circle(
        (400, 300), 50, linewidth=2, edgecolor="red", facecolor="lightcoral", alpha=0.7
    )
    ax.add_patch(circle1)
    ax.text(
        400, 300, "禁飞区1", ha="center", va="center", fontsize=10, fontweight="bold"
    )

    # 禁飞区2 (矩形)
    rect2 = patches.Rectangle(
        (300, 600),
        150,
        100,
        linewidth=2,
        edgecolor="red",
        facecolor="lightcoral",
        alpha=0.7,
    )
    ax.add_patch(rect2)
    ax.text(
        375, 650, "禁飞区2", ha="center", va="center", fontsize=10, fontweight="bold"
    )

    # 禁飞区3 (圆形)
    circle3 = patches.Circle(
        (450, 650), 40, linewidth=2, edgecolor="red", facecolor="lightcoral", alpha=0.7
    )
    ax.add_patch(circle3)
    ax.text(
        450, 650, "禁飞区3", ha="center", va="center", fontsize=10, fontweight="bold"
    )

    # 绘制轨道
    colors = ["blue", "green", "purple"]
    zone_names = ["zone1", "zone2", "zone3"]

    for i, zone_name in enumerate(zone_names):
        orbit_points = mock_map.obstacle_manager.get_orbit_path(zone_name)
        if orbit_points:
            # 转换坐标 (y,x) -> (x,y) 用于绘图
            orbit_x = [point[1] for point in orbit_points]
            orbit_y = [point[0] for point in orbit_points]

            # 闭合轨道
            orbit_x.append(orbit_x[0])
            orbit_y.append(orbit_y[0])

            ax.plot(
                orbit_x,
                orbit_y,
                "o-",
                color=colors[i],
                linewidth=2,
                markersize=4,
                label=f"{zone_name}轨道",
                alpha=0.8,
            )

            # 标记轨道点索引
            for j, (x, y) in enumerate(zip(orbit_x[:-1], orbit_y[:-1])):
                if j % 4 == 0:  # 每4个点标记一个索引
                    ax.text(
                        x,
                        y,
                        str(j),
                        fontsize=8,
                        ha="center",
                        va="center",
                        bbox=dict(
                            boxstyle="round,pad=0.1", facecolor="white", alpha=0.7
                        ),
                    )

    ax.set_xlim(0, 1000)
    ax.set_ylim(0, 1000)
    ax.set_xlabel("X坐标")
    ax.set_ylabel("Y坐标")
    ax.set_title("轨道路径规划地图 - 禁飞区和轨道可视化")
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_aspect("equal")

    plt.tight_layout()
    plt.savefig("orbit_map_visualization.png", dpi=300, bbox_inches="tight")
    print("地图可视化已保存为 'orbit_map_visualization.png'")
    plt.close()


def test_and_visualize_pathfinding():
    """测试并可视化路径规划结果"""
    print("开始轨道路径规划可视化测试...")

    # 创建模拟地图
    mock_map = MockMap3D()

    # 创建轨道路径规划器
    orbit_finder = OrbitWithAStarPathFinder(mock_map)

    # 定义测试场景
    test_cases = [
        # {
        #     "name": "场景1: 穿越单个禁飞区",
        #     "start": (100, 100, 50),
        #     "goal": (500, 600, 50),
        #     "min_height": 50,
        # },
        # {
        #     "name": "场景2: 穿越多个禁飞区",
        #     "start": (200, 200, 50),
        #     "goal": (600, 800, 50),
        #     "min_height": 50,
        # },
        # {
        #     "name": "场景3: 复杂路径",
        #     "start": (800, 200, 50),
        #     "goal": (600, 600, 50),
        #     "min_height": 50,
        # },
        {
            "name": "drone_089",
            "start": (770, 141, 4),
            "goal": (368, 696, 22),
            "min_height": 50,
        },
    ]

    # 为每个测试场景生成路径并可视化
    for i, test_case in enumerate(test_cases):
        print(f"\n测试 {test_case['name']}")

        # 记录开始时间
        start_time = time.time()

        # 执行路径规划
        path, error = orbit_finder.find_path(
            start=test_case["start"],
            goal=test_case["goal"],
            min_height=test_case["min_height"],
            agent_id=f"drone_{i}",
            start_time=0,
        )

        # 计算耗时
        time_cost = time.time() - start_time

        if path:
            print(f"✓ 路径规划成功，路径长度: {len(path)} 个节点")
            print(f"  规划耗时: {time_cost:.3f} 秒")
            visualize_single_path(mock_map, path, test_case, i)
        else:
            print(f"✗ 路径规划失败: {error}")


def visualize_single_path(mock_map, path, test_case, case_index):
    """可视化单个路径规划结果"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))

    # 绘制禁飞区
    # 禁飞区1 (圆形)
    circle1 = patches.Circle(
        (400, 300), 50, linewidth=2, edgecolor="red", facecolor="lightcoral", alpha=0.7
    )
    ax.add_patch(circle1)

    # 禁飞区2 (矩形)
    rect2 = patches.Rectangle(
        (300, 600),
        150,
        100,
        linewidth=2,
        edgecolor="red",
        facecolor="lightcoral",
        alpha=0.7,
    )
    ax.add_patch(rect2)

    # 禁飞区3 (圆形)
    circle3 = patches.Circle(
        (450, 650), 40, linewidth=2, edgecolor="red", facecolor="lightcoral", alpha=0.7
    )
    ax.add_patch(circle3)

    # 绘制轨道（淡化显示）
    colors = ["lightblue", "lightgreen", "plum"]
    zone_names = ["zone1", "zone2", "zone3"]

    for i, zone_name in enumerate(zone_names):
        orbit_points = mock_map.obstacle_manager.get_orbit_path(zone_name)
        if orbit_points:
            orbit_x = [point[1] for point in orbit_points]
            orbit_y = [point[0] for point in orbit_points]
            orbit_x.append(orbit_x[0])
            orbit_y.append(orbit_y[0])

            ax.plot(orbit_x, orbit_y, "--", color=colors[i], linewidth=1, alpha=0.5)

    # 绘制规划路径
    if path:
        path_x = [node.x for node in path]
        path_y = [node.y for node in path]

        # 分析路径阶段
        min_height = test_case["min_height"]
        takeoff_nodes = [(node.x, node.y) for node in path if node.z < min_height]
        cruise_nodes = [(node.x, node.y) for node in path if node.z >= min_height]

        # 绘制起飞阶段
        if takeoff_nodes:
            takeoff_x, takeoff_y = zip(*takeoff_nodes)
            ax.plot(
                takeoff_x,
                takeoff_y,
                "o-",
                color="orange",
                linewidth=3,
                markersize=6,
                label="起飞阶段",
                alpha=0.8,
            )

        # 绘制巡航阶段
        if cruise_nodes:
            cruise_x, cruise_y = zip(*cruise_nodes)
            ax.plot(
                cruise_x,
                cruise_y,
                "o-",
                color="blue",
                linewidth=3,
                markersize=4,
                label="巡航阶段",
                alpha=0.8,
            )

        # 标记起点和终点
        start_node = path[0]
        end_node = path[-1]

        ax.plot(
            start_node.x,
            start_node.y,
            "s",
            color="green",
            markersize=12,
            label="起点",
            markeredgecolor="black",
            markeredgewidth=2,
        )
        ax.plot(
            end_node.x,
            end_node.y,
            "^",
            color="red",
            markersize=12,
            label="终点",
            markeredgecolor="black",
            markeredgewidth=2,
        )

        # 添加箭头显示方向
        for i in range(0, len(path_x) - 1, max(1, len(path_x) // 10)):
            dx = path_x[i + 1] - path_x[i]
            dy = path_y[i + 1] - path_y[i]
            if dx != 0 or dy != 0:
                ax.arrow(
                    path_x[i],
                    path_y[i],
                    dx * 0.7,
                    dy * 0.7,
                    head_width=15,
                    head_length=10,
                    fc="darkblue",
                    ec="darkblue",
                    alpha=0.6,
                )

    # 添加路径信息
    info_text = f"""
路径信息:
起点: ({test_case['start'][1]}, {test_case['start'][0]})
终点: ({test_case['goal'][1]}, {test_case['goal'][0]})
路径长度: {len(path)} 个节点
最小巡航高度: {test_case['min_height']}
"""

    ax.text(
        0.02,
        0.98,
        info_text,
        transform=ax.transAxes,
        fontsize=10,
        verticalalignment="top",
        bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8),
    )

    ax.set_xlim(0, 1000)
    ax.set_ylim(0, 1000)
    ax.set_xlabel("X坐标")
    ax.set_ylabel("Y坐标")
    ax.set_title(f'{test_case["name"]} - 轨道路径规划结果')
    ax.legend(loc="upper right")
    ax.grid(True, alpha=0.3)
    ax.set_aspect("equal")

    plt.tight_layout()
    filename = f"orbit_path_case_{case_index + 1}.png"
    plt.savefig(filename, dpi=300, bbox_inches="tight")
    print(f"路径可视化已保存为 '{filename}'")
    plt.close()


def main():
    """主函数"""
    print("=" * 60)
    print("轨道路径规划算法可视化测试")
    print("=" * 60)

    # 创建模拟地图
    mock_map = MockMap3D()

    # 首先显示地图和轨道
    print("1. 显示地图和轨道布局...")
    visualize_map_and_orbits(mock_map)

    # 然后测试并可视化路径规划
    print("\n2. 测试并可视化路径规划结果...")
    test_and_visualize_pathfinding()

    print("\n" + "=" * 60)
    print("可视化测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
